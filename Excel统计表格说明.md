# 园区企业案件统计表格说明

## 概述
根据 `规则.md` 的要求，已创建包含7个工作表的Excel统计表格，每个工作表对应一个特定的统计需求。

## 工作表详细说明

### 工作表1：各案由案件分布情况
**文件名**: `1.各案由案件分布情况`
**结构**: 
- 列A: 案由
- 列B: 案件数量

**说明**: 按案件数量从高到低排列各类案由，便于快速了解园区企业最常见的法律纠纷类型。

### 工作表2：一审二审案件分布
**文件名**: `2.一审二审案件分布`
**结构**: 
- 列A: 案由
- 列B: 一审案件数
- 列C: 二审案件数  
- 列D: 一审:二审比例

**说明**: 统计各案由在一审、二审阶段的案件分布情况，帮助分析案件的审理层级分布。

### 工作表3：一审二审胜败诉分布
**文件名**: `3.一审二审胜败诉分布`
**结构**: 
- 列A: 案由
- 列B: 一审胜诉数量
- 列C: 一审败诉数量
- 列D: 一审胜诉率
- 列E: 二审胜诉数量
- 列F: 二审败诉数量
- 列G: 二审胜诉率

**说明**: 详细统计各案由在一审、二审阶段的胜败诉情况，为企业法律风险评估提供数据支持。

### 工作表4：企业规模分布
**文件名**: `4.企业规模分布`
**结构**: 
- 列A: 企业规模（中型/小型）
- 列B: 企业数量
- 列C: 占比

**说明**: 统计园区内中型企业和小型企业的分布比例。

### 工作表5：不同年份纠纷分布
**文件名**: `5.不同年份纠纷分布`
**结构**: 
- 列A: 案由类型
- 列B: 2021年案件数
- 列C: 2022年案件数
- 列D: 2023年案件数
- 列E: 2024年案件数

**说明**: 按年份统计各类纠纷的分布情况，每年的数据按案件数量从高到低排列，便于分析纠纷趋势变化。

### 工作表6：不同行业纠纷分布
**文件名**: `6.不同行业纠纷分布`
**结构**: 
- 列A: 行业类型
- 列B: 纠纷案件数
- 列C: 占比

**说明**: 统计不同行业的纠纷分布情况，帮助识别高风险行业。

### 工作表7：不同企业规模案件分布
**文件名**: `7.不同企业规模案件分布`
**结构**: 
- 列A: 企业规模
- 列B: 案件数量
- 列C: 占比

**说明**: 专门统计中小型企业的案件数量分布情况。

## 数据填充说明

### 当前状态
- 所有工作表已创建完成，包含示例数据
- 表格已进行专业格式化，包括：
  - 表头使用深蓝色背景和白色字体
  - 所有单元格添加边框
  - 数据居中对齐
  - 列宽自动调整

### 数据更新方式
1. **手动更新**: 直接在Excel中编辑各工作表的数据
2. **程序更新**: 修改 `园区企业案件统计表.py` 中的示例数据，重新运行生成

### 统计建议
根据规则.md的提示，建议采用以下统计方式：
1. **大案由统计**: 先统计主要案由类别的总数量
2. **细分案由**: 在主要案由下面列出更具体的子案由进行计数
3. **数据排序**: 所有统计数据按数量从高到低排列
4. **比例计算**: 重要指标需要计算占比，便于分析

## 使用建议
1. 定期更新数据以保持统计的时效性
2. 可根据实际需要调整案由分类的粒度
3. 建议结合图表功能进行数据可视化展示
4. 可以基于这些统计数据进行深度分析和报告撰写

## 技术说明
- 使用Python的openpyxl库生成Excel文件
- 支持专业的表格格式化
- 代码结构清晰，便于维护和扩展
- 可以轻松添加新的统计维度或修改现有结构
