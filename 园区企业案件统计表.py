#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
园区企业案件统计表生成器
根据规则.md要求创建包含7个工作表的Excel统计表格
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

class CaseStatisticsGenerator:
    def __init__(self):
        self.wb = Workbook()
        # 删除默认工作表
        self.wb.remove(self.wb.active)
        
    def create_sheet1_case_distribution(self):
        """工作表1：园区企业各案由案件分布情况"""
        ws = self.wb.create_sheet("1.各案由案件分布情况")
        
        # 设置表头
        headers = ["案由", "案件数量"]
        ws.append(headers)
        
        # 示例数据（实际使用时需要替换为真实数据）
        sample_data = [
            ["合同纠纷", 150],
            ["劳动争议", 120],
            ["知识产权纠纷", 80],
            ["公司纠纷", 65],
            ["侵权责任纠纷", 45],
            ["借款合同纠纷", 40],
            ["买卖合同纠纷", 35],
            ["租赁合同纠纷", 30],
            ["建设工程合同纠纷", 25],
            ["其他", 20]
        ]
        
        for row in sample_data:
            ws.append(row)
            
        self._format_sheet(ws, len(headers))
        
    def create_sheet2_trial_distribution(self):
        """工作表2：园区企业各案由案件一审、二审案件分布情况"""
        ws = self.wb.create_sheet("2.一审二审案件分布")
        
        # 设置表头
        headers = ["案由", "一审案件数", "二审案件数", "一审:二审比例"]
        ws.append(headers)
        
        # 示例数据
        sample_data = [
            ["合同纠纷", 120, 30, "4:1"],
            ["劳动争议", 100, 20, "5:1"],
            ["知识产权纠纷", 65, 15, "4.3:1"],
            ["公司纠纷", 50, 15, "3.3:1"],
            ["侵权责任纠纷", 35, 10, "3.5:1"],
            ["借款合同纠纷", 32, 8, "4:1"],
            ["买卖合同纠纷", 28, 7, "4:1"],
            ["租赁合同纠纷", 24, 6, "4:1"],
            ["建设工程合同纠纷", 20, 5, "4:1"],
            ["其他", 16, 4, "4:1"]
        ]
        
        for row in sample_data:
            ws.append(row)
            
        self._format_sheet(ws, len(headers))
        
    def create_sheet3_win_lose_distribution(self):
        """工作表3：园区企业各案由案件一审、二审胜败诉分布情况"""
        ws = self.wb.create_sheet("3.一审二审胜败诉分布")
        
        # 设置表头
        headers = ["案由", "一审胜诉", "一审败诉", "一审胜诉率", "二审胜诉", "二审败诉", "二审胜诉率"]
        ws.append(headers)
        
        # 示例数据
        sample_data = [
            ["合同纠纷", 72, 48, "60%", 18, 12, "60%"],
            ["劳动争议", 50, 50, "50%", 10, 10, "50%"],
            ["知识产权纠纷", 45, 20, "69%", 10, 5, "67%"],
            ["公司纠纷", 30, 20, "60%", 9, 6, "60%"],
            ["侵权责任纠纷", 21, 14, "60%", 6, 4, "60%"],
            ["借款合同纠纷", 24, 8, "75%", 6, 2, "75%"],
            ["买卖合同纠纷", 20, 8, "71%", 5, 2, "71%"],
            ["租赁合同纠纷", 16, 8, "67%", 4, 2, "67%"],
            ["建设工程合同纠纷", 12, 8, "60%", 3, 2, "60%"],
            ["其他", 10, 6, "63%", 2, 2, "50%"]
        ]
        
        for row in sample_data:
            ws.append(row)
            
        self._format_sheet(ws, len(headers))
        
    def create_sheet4_company_scale_distribution(self):
        """工作表4：园区企业规模分布"""
        ws = self.wb.create_sheet("4.企业规模分布")

        # 设置表头
        headers = ["企业规模", "企业数量", "占比"]
        ws.append(headers)

        # 示例数据 - 企业规模分布（通常小型企业占大多数）
        sample_data = [
            ["小型企业", 280, "82%"],
            ["中型企业", 60, "18%"],
            ["总计", 340, "100%"]
        ]

        for row in sample_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def create_sheet5_yearly_distribution(self):
        """工作表5：不同年份纠纷分布情况"""
        ws = self.wb.create_sheet("5.不同年份纠纷分布")
        
        # 设置表头
        headers = ["案由类型", "2021年", "2022年", "2023年", "2024年"]
        ws.append(headers)
        
        # 示例数据（按年份从高到低排列）
        sample_data = [
            ["合同纠纷", 35, 40, 45, 30],
            ["劳动争议", 30, 35, 35, 20],
            ["知识产权纠纷", 15, 20, 25, 20],
            ["公司纠纷", 12, 18, 20, 15],
            ["侵权责任纠纷", 10, 12, 15, 8],
            ["借款合同纠纷", 8, 10, 12, 10],
            ["买卖合同纠纷", 6, 8, 12, 9],
            ["租赁合同纠纷", 5, 7, 10, 8],
            ["建设工程合同纠纷", 4, 6, 8, 7],
            ["其他", 3, 5, 7, 5]
        ]
        
        for row in sample_data:
            ws.append(row)
            
        self._format_sheet(ws, len(headers))
        
    def create_sheet6_industry_distribution(self):
        """工作表6：不同行业纠纷分布情况"""
        ws = self.wb.create_sheet("6.不同行业纠纷分布")
        
        # 设置表头
        headers = ["行业类型", "纠纷案件数", "占比"]
        ws.append(headers)
        
        # 示例数据
        sample_data = [
            ["制造业", 180, "30%"],
            ["信息技术服务业", 120, "20%"],
            ["批发和零售业", 90, "15%"],
            ["建筑业", 72, "12%"],
            ["租赁和商务服务业", 60, "10%"],
            ["交通运输、仓储和邮政业", 36, "6%"],
            ["金融业", 24, "4%"],
            ["其他", 18, "3%"]
        ]
        
        for row in sample_data:
            ws.append(row)
            
        self._format_sheet(ws, len(headers))
        
    def create_sheet7_scale_case_distribution(self):
        """工作表7：园区不同企业规模的案件分布情况"""
        ws = self.wb.create_sheet("7.不同企业规模案件分布")
        
        # 设置表头
        headers = ["企业规模", "案件数量", "占比"]
        ws.append(headers)
        
        # 示例数据
        sample_data = [
            ["中型企业", 210, "35%"],
            ["小型企业", 390, "65%"],
            ["总计", 600, "100%"]
        ]
        
        for row in sample_data:
            ws.append(row)
            
        self._format_sheet(ws, len(headers))
        
    def _format_sheet(self, ws, num_columns):
        """格式化工作表"""
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 设置边框
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 应用表头样式
        for col in range(1, num_columns + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border
            
        # 设置列宽
        for col in range(1, num_columns + 1):
            ws.column_dimensions[chr(64 + col)].width = 20
            
        # 为所有数据行添加边框和居中对齐
        for row in range(1, ws.max_row + 1):
            for col in range(1, num_columns + 1):
                cell = ws.cell(row=row, column=col)
                cell.border = thin_border
                if row > 1:  # 数据行居中对齐
                    cell.alignment = Alignment(horizontal="center", vertical="center")
    
    def generate_excel(self, filename="园区企业案件统计表.xlsx"):
        """生成Excel文件"""
        # 创建所有工作表
        self.create_sheet1_case_distribution()
        self.create_sheet2_trial_distribution()
        self.create_sheet3_win_lose_distribution()
        self.create_sheet4_company_scale_distribution()
        self.create_sheet5_yearly_distribution()
        self.create_sheet6_industry_distribution()
        self.create_sheet7_scale_case_distribution()
        
        # 保存文件
        self.wb.save(filename)
        print(f"Excel统计表格已生成：{filename}")
        
        return filename

if __name__ == "__main__":
    generator = CaseStatisticsGenerator()
    generator.generate_excel()
