#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
园区企业案件统计表生成器
根据规则.md要求创建包含7个工作表的Excel统计表格
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

class CaseStatisticsGenerator:
    def __init__(self, data_file="修正审级的企业案件数据_20250812_123809.xlsx"):
        self.wb = Workbook()
        # 删除默认工作表
        self.wb.remove(self.wb.active)
        # 加载真实数据
        self.df = pd.read_excel(data_file)
        print(f"已加载数据：{len(self.df)}条记录")
        
    def create_sheet1_case_distribution(self):
        """工作表1：园区企业各案由案件分布情况"""
        ws = self.wb.create_sheet("1.各案由案件分布情况")

        # 设置表头
        headers = ["案由", "案件数量"]
        ws.append(headers)

        # 基于真实数据统计案由分布
        case_counts = self.df['案由'].value_counts()

        # 清理案由数据，去除方括号
        cleaned_data = []
        for case_type, count in case_counts.items():
            # 去除方括号
            clean_case_type = str(case_type).strip("[]'\"")
            cleaned_data.append([clean_case_type, count])

        # 按数量从高到低排序
        cleaned_data.sort(key=lambda x: x[1], reverse=True)

        for row in cleaned_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def create_sheet2_trial_distribution(self):
        """工作表2：园区企业各案由案件一审、二审案件分布情况"""
        ws = self.wb.create_sheet("2.一审二审案件分布")

        # 设置表头
        headers = ["案由", "一审案件数", "二审案件数", "一审:二审比例"]
        ws.append(headers)

        # 基于真实数据统计一审二审分布
        trial_data = self.df.groupby(['案由', '审级']).size().unstack(fill_value=0)

        # 只保留一审和二审数据
        if '一审' not in trial_data.columns:
            trial_data['一审'] = 0
        if '二审' not in trial_data.columns:
            trial_data['二审'] = 0

        result_data = []
        for case_type in trial_data.index:
            first_trial = trial_data.loc[case_type, '一审']
            second_trial = trial_data.loc[case_type, '二审']

            # 计算比例
            if second_trial > 0:
                ratio = f"{first_trial}:{second_trial}"
            else:
                ratio = f"{first_trial}:0" if first_trial > 0 else "0:0"

            # 清理案由名称
            clean_case_type = str(case_type).strip("[]'\"")
            result_data.append([clean_case_type, first_trial, second_trial, ratio])

        # 按一审案件数从高到低排序
        result_data.sort(key=lambda x: x[1], reverse=True)

        for row in result_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def create_sheet3_win_lose_distribution(self):
        """工作表3：园区企业各案由案件一审、二审胜败诉分布情况"""
        ws = self.wb.create_sheet("3.一审二审胜败诉分布")

        # 设置表头
        headers = ["案由", "一审胜诉", "一审败诉", "一审胜诉率", "二审胜诉", "二审败诉", "二审胜诉率"]
        ws.append(headers)

        # 定义胜诉和败诉的判断标准
        win_results = ['支持全部诉讼请求', '确认判决']
        lose_results = ['驳回全部诉讼请求', '驳回上诉请求']

        # 基于真实数据统计胜败诉分布
        result_data = []

        # 按案由分组统计
        for case_type in self.df['案由'].unique():
            case_df = self.df[self.df['案由'] == case_type]

            # 一审数据
            first_trial_df = case_df[case_df['审级'] == '一审']
            first_win = len(first_trial_df[first_trial_df['诉讼结果'].isin(win_results)])
            first_lose = len(first_trial_df[first_trial_df['诉讼结果'].isin(lose_results)])
            first_total = first_win + first_lose
            first_win_rate = f"{first_win/first_total*100:.1f}%" if first_total > 0 else "/"

            # 二审数据
            second_trial_df = case_df[case_df['审级'] == '二审']
            second_win = len(second_trial_df[second_trial_df['诉讼结果'].isin(win_results)])
            second_lose = len(second_trial_df[second_trial_df['诉讼结果'].isin(lose_results)])
            second_total = second_win + second_lose
            second_win_rate = f"{second_win/second_total*100:.1f}%" if second_total > 0 else "/"

            # 清理案由名称
            clean_case_type = str(case_type).strip("[]'\"")

            result_data.append([
                clean_case_type, first_win, first_lose, first_win_rate,
                second_win, second_lose, second_win_rate
            ])

        # 按一审案件总数排序
        result_data.sort(key=lambda x: x[1] + x[2], reverse=True)

        for row in result_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def create_sheet4_company_scale_distribution(self):
        """工作表4：园区企业规模分布"""
        ws = self.wb.create_sheet("4.企业规模分布")

        # 设置表头
        headers = ["企业规模", "企业数量", "占比"]
        ws.append(headers)

        # 基于真实数据统计企业规模分布（按企业去重）
        company_scale = self.df.drop_duplicates(subset=['公司名称'])['企业规模'].value_counts()
        total_companies = company_scale.sum()

        result_data = []
        for scale, count in company_scale.items():
            percentage = f"{count/total_companies*100:.1f}%"
            result_data.append([scale, count, percentage])

        # 按数量从高到低排序
        result_data.sort(key=lambda x: x[1], reverse=True)

        # 添加总计行
        result_data.append(["总计", total_companies, "100.0%"])

        for row in result_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def create_sheet5_yearly_distribution(self):
        """工作表5：不同年份纠纷分布情况"""
        ws = self.wb.create_sheet("5.不同年份纠纷分布")

        # 先添加每年总案件数统计
        yearly_totals = self.df['年份'].value_counts().sort_index()

        # 添加年度总计行
        total_headers = ["年份", "2021年", "2022年", "2023年", "2024年"]
        ws.append(total_headers)

        total_row = ["总案件数"]
        for year in [2021, 2022, 2023, 2024]:
            total_count = yearly_totals.get(year, 0)
            total_row.append(total_count)
        ws.append(total_row)

        # 添加空行分隔
        ws.append([])

        # 设置案由分布表头
        headers = ["案由类型", "2021年", "2022年", "2023年", "2024年"]
        ws.append(headers)

        # 基于真实数据统计年份分布
        yearly_data = self.df.groupby(['案由', '年份']).size().unstack(fill_value=0)

        # 确保所有年份列都存在
        for year in [2021, 2022, 2023, 2024]:
            if year not in yearly_data.columns:
                yearly_data[year] = 0

        result_data = []
        for case_type in yearly_data.index:
            clean_case_type = str(case_type).strip("[]'\"")
            row_data = [
                clean_case_type,
                yearly_data.loc[case_type, 2021],
                yearly_data.loc[case_type, 2022],
                yearly_data.loc[case_type, 2023],
                yearly_data.loc[case_type, 2024]
            ]
            result_data.append(row_data)

        # 按总案件数从高到低排序
        result_data.sort(key=lambda x: sum(x[1:]), reverse=True)

        for row in result_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def create_sheet6_industry_distribution(self):
        """工作表6：不同行业纠纷分布情况"""
        ws = self.wb.create_sheet("6.不同行业纠纷分布")

        # 设置表头
        headers = ["行业类型", "纠纷案件数", "占比"]
        ws.append(headers)

        # 基于真实数据统计行业分布
        industry_counts = self.df['国标行业门类'].value_counts()
        total_cases = industry_counts.sum()

        result_data = []
        for industry, count in industry_counts.items():
            percentage = f"{count/total_cases*100:.1f}%"
            result_data.append([industry, count, percentage])

        # 数据已经按数量从高到低排序
        for row in result_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def create_sheet7_scale_case_distribution(self):
        """工作表7：园区不同企业规模的案件分布情况"""
        ws = self.wb.create_sheet("7.不同企业规模案件分布")

        # 设置表头
        headers = ["企业规模", "案件数量", "占比"]
        ws.append(headers)

        # 基于真实数据统计企业规模的案件分布
        scale_case_counts = self.df['企业规模'].value_counts()
        total_cases = scale_case_counts.sum()

        result_data = []
        for scale, count in scale_case_counts.items():
            percentage = f"{count/total_cases*100:.1f}%"
            result_data.append([scale, count, percentage])

        # 按案件数量从高到低排序
        result_data.sort(key=lambda x: x[1], reverse=True)

        # 添加总计行
        result_data.append(["总计", total_cases, "100.0%"])

        for row in result_data:
            ws.append(row)

        self._format_sheet(ws, len(headers))
        
    def _format_sheet(self, ws, num_columns):
        """格式化工作表"""
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 设置边框
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 应用表头样式
        for col in range(1, num_columns + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border
            
        # 设置列宽
        for col in range(1, num_columns + 1):
            ws.column_dimensions[chr(64 + col)].width = 20
            
        # 为所有数据行添加边框和居中对齐
        for row in range(1, ws.max_row + 1):
            for col in range(1, num_columns + 1):
                cell = ws.cell(row=row, column=col)
                cell.border = thin_border
                if row > 1:  # 数据行居中对齐
                    cell.alignment = Alignment(horizontal="center", vertical="center")
    
    def generate_excel(self, filename="园区企业案件统计表.xlsx"):
        """生成Excel文件"""
        # 创建所有工作表
        self.create_sheet1_case_distribution()
        self.create_sheet2_trial_distribution()
        self.create_sheet3_win_lose_distribution()
        self.create_sheet4_company_scale_distribution()
        self.create_sheet5_yearly_distribution()
        self.create_sheet6_industry_distribution()
        self.create_sheet7_scale_case_distribution()
        
        # 保存文件
        self.wb.save(filename)
        print(f"Excel统计表格已生成：{filename}")
        
        return filename

if __name__ == "__main__":
    generator = CaseStatisticsGenerator()
    generator.generate_excel()
